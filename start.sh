#!/usr/bin/env bash
set -e

# X<PERSON><PERSON> định thư mục gốc của dự án
tmp_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="${tmp_dir}"

# Hàm dọn dẹp khi kết thúc script
echo "🔧 Đăng ký hàm cleanup để tắt PathRAG khi script kết thúc"
cleanup() {
  echo "🛑 Stopping PathRAG server (PID ${PATHRAG_PID})..."
  kill "${PATHRAG_PID}" 2>/dev/null || true
}
trap cleanup EXIT

# Cài đặt dependencies cho PathRAG
echo "🚀 Installing PathRAG dependencies..."
pip3 install -r "${ROOT_DIR}/pathrag/requirements.txt"

# Khởi động PathRAG API server ở background
echo "🚀 Starting PathRAG API server..."
cd "${ROOT_DIR}/pathrag"
python3 pathrag_api.py &
PATHRAG_PID=$!

echo "PathRAG PID: ${PATHRAG_PID}"

# Quay lại thư mục gốc
echo "🔙 Returning to project root"
cd "${ROOT_DIR}"

# Nạp biến môi trường từ .env nếu có
if [[ -f "${ROOT_DIR}/.env" ]]; then
  echo "🔑 Loading environment variables from .env..."
  set -a
  source "${ROOT_DIR}/.env"
  set +a
else
  echo "⚠️ .env file not found. Please create .env based on .env.example"
fi

# Đảm bảo PATHRAG_API_URL được thiết lập
export PATHRAG_API_URL="${PATHRAG_API_URL:-http://localhost:8123/api/context}"
echo "🌐 Using PATHRAG_API_URL=${PATHRAG_API_URL}"

# Cài đặt dependencies cho dự án Node.js
echo "📦 Installing JS dependencies..."
pnpm install
echo "🔨 Đặt NODE_ENV=production để build Next.js"
export NODE_ENV=production

# Build ứng dụng Next.js
echo "🏗️ Building Next.js application..."
pnpm build

# Khởi động Next.js development server
echo "🚀 Starting cs_agent (Next.js) development server..."
pnpm start
