version: '3.8'

services:
  cs-agent:
    build: .
    ports:
      - "3000:3000"
      - "8123:8123"
    environment:
      # Authentication
      - AUTH_SECRET=${AUTH_SECRET:-cs_agent}
      
      # iframe Authentication Configuration
      - IFRAME_JWT_SECRET=${IFRAME_JWT_SECRET:-cs_agent_iframe_secret_key_2024}
      - IFRAME_TOKEN_EXPIRY=${IFRAME_TOKEN_EXPIRY:-15m}
      - IFRAME_ALLOWED_ORIGINS=${IFRAME_ALLOWED_ORIGINS:-http://localhost:*,https://localhost:*}
      
      # API Keys
      - GROQ_API_KEY=${GROQ_API_KEY}
      - GOOGLE_GENERATIVE_AI_API_KEY=${GOOGLE_GENERATIVE_AI_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      
      # Storage
      - BLOB_READ_WRITE_TOKEN=${BLOB_READ_WRITE_TOKEN}
      
      # Database
      - POSTGRES_URL=${POSTGRES_URL}
      - POSTGRES_PRISMA_URL=${POSTGRES_PRISMA_URL}
      - POSTGRES_URL_NON_POOLING=${POSTGRES_URL_NON_POOLING}
      
      # PathRAG Configuration
      - PATHRAG_API_URL=http://localhost:8123/api/context
      
      # Observability
      - LANGFUSE_SECRET_KEY=${LANGFUSE_SECRET_KEY}
      - LANGFUSE_PUBLIC_KEY=${LANGFUSE_PUBLIC_KEY}
      - LANGFUSE_BASEURL=${LANGFUSE_BASEURL:-https://cloud.langfuse.com}
      
      # Redis Cache
      - REDIS_URL=${REDIS_URL}
      - REDIS_TOKEN=${REDIS_TOKEN}
      
      # Google Cloud
      - GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT}
      - GOOGLE_CLOUD_LOCATION=${GOOGLE_CLOUD_LOCATION:-us-central1}
      
      # Cache Configuration
      - CACHE_ENABLED=${CACHE_ENABLED:-true}
      - CACHE_DEFAULT_TTL=${CACHE_DEFAULT_TTL:-86400}
      - CACHE_MAX_SIZE=${CACHE_MAX_SIZE:-10485760}
      
      # Application Configuration
      - NODE_ENV=production
      - NEXTAUTH_URL=${NEXTAUTH_URL:-http://localhost:3000}
    
    volumes:
      # Mount knowledge base data if needed
      - ./pathrag/knowledgebase:/app/pathrag/knowledgebase:ro
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health", "||", "exit", "1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
