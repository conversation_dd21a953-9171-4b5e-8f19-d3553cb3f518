# 🐳 CS Agent Docker - Hướng dẫn nhanh

## Bước 1: <PERSON>ài đặt Docker

### macOS
```bash
# Tải Docker Desktop từ: https://www.docker.com/products/docker-desktop
# Hoặc dùng Homebrew:
brew install --cask docker
```

### Ubuntu/Debian
```bash
sudo apt update
sudo apt install docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER
```

### CentOS/RHEL
```bash
sudo yum install docker docker-compose
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER
```

## Bước 2: Khởi động Docker

```bash
# Kiểm tra Docker đã chạy chưa
docker --version
docker-compose --version

# Nếu chưa chạy, khởi động Docker Desktop (macOS/Windows)
# Hoặc khởi động service (Linux):
sudo systemctl start docker
```

## Bước 3: Build và chạy CS Agent

```bash
# Chuyển đến thư mục dự án
cd /path/to/cs_agent

# Cách 1: Sử dụng script build (khuyến nghị)
./docker-build.sh

# Cách 2: Build và chạy với docker-compose
docker-compose up --build -d

# Cách 3: Build và chạy thủ công
docker build -t cs-agent .
docker run -p 3000:3000 -p 8123:8123 --env-file .env cs-agent
```

## Bước 4: Kiểm tra hoạt động

- **Web Interface**: http://localhost:3000
- **PathRAG API**: http://localhost:8123/api/context

## Lệnh hữu ích

```bash
# Xem logs
docker-compose logs -f

# Dừng container
docker-compose down

# Restart container
docker-compose restart

# Xem status
docker-compose ps

# Vào container để debug
docker-compose exec cs-agent bash
```

## Troubleshooting

### Lỗi "Docker is not running"
```bash
# macOS: Mở Docker Desktop
# Linux: 
sudo systemctl start docker
```

### Lỗi "Permission denied"
```bash
# Thêm user vào group docker
sudo usermod -aG docker $USER
# Logout và login lại
```

### Port đã được sử dụng
```bash
# Kiểm tra port nào đang sử dụng
lsof -i :3000
lsof -i :8123

# Thay đổi port trong docker-compose.yml
ports:
  - "8080:3000"  # Thay vì 3000:3000
  - "8124:8123"  # Thay vì 8123:8123
```

### Container không build được
```bash
# Xóa cache và build lại
docker system prune -a
docker-compose build --no-cache
```

## Deploy trên máy khác

1. **Copy toàn bộ thư mục dự án** sang máy mới
2. **Cài đặt Docker** trên máy mới
3. **Chạy lệnh build**:
   ```bash
   docker-compose up --build -d
   ```

## Backup và Restore

### Backup
```bash
# Backup toàn bộ dự án
tar -czf cs-agent-backup.tar.gz /path/to/cs_agent

# Backup chỉ knowledge base
docker-compose exec cs-agent tar -czf /tmp/kb-backup.tar.gz -C /app/pathrag knowledgebase
docker cp $(docker-compose ps -q cs-agent):/tmp/kb-backup.tar.gz ./
```

### Restore
```bash
# Restore toàn bộ
tar -xzf cs-agent-backup.tar.gz

# Restore knowledge base
docker cp ./kb-backup.tar.gz $(docker-compose ps -q cs-agent):/tmp/
docker-compose exec cs-agent tar -xzf /tmp/kb-backup.tar.gz -C /app/pathrag
```
