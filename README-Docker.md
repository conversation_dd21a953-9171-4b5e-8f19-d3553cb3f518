# CS Agent Docker Setup

Hướng dẫn chạy CS Agent bằng Docker để có thể deploy trên máy khác.

## <PERSON><PERSON><PERSON> c<PERSON>u

- Docker
- Docker Compose
- File `.env` với các biến môi trường cần thiết

## C<PERSON>ch sử dụng

### 1. Chuẩn bị file .env

Tạo file `.env` trong thư mục gốc với các biến môi trường cần thiết:

```bash
# Copy từ .env hiện tại hoặc tạo mới
cp .env .env.docker
```

### 2. Build và chạy với Docker Compose (Khuyến nghị)

```bash
# Build và chạy
docker-compose up --build

# Chạy ở background
docker-compose up -d --build

# Xem logs
docker-compose logs -f

# Dừng
docker-compose down
```

### 3. Build và chạy với Docker trực tiếp

```bash
# Build image
docker build -t cs-agent .

# Chạy container
docker run -p 3000:3000 -p 8123:8123 --env-file .env cs-agent
```

## Ports

- **3000**: Next.js Application (Web Interface)
- **8123**: PathRAG API (Knowledge Base)

## Kiểm tra hoạt động

1. Truy cập http://localhost:3000 để sử dụng web interface
2. Kiểm tra PathRAG API: http://localhost:8123/api/context

## Troubleshooting

### Container không khởi động được

```bash
# Xem logs chi tiết
docker-compose logs cs-agent

# Kiểm tra container status
docker-compose ps
```

### PathRAG API không hoạt động

```bash
# Kiểm tra logs PathRAG
docker-compose exec cs-agent tail -f /app/pathrag/*.log

# Test PathRAG API
curl http://localhost:8123/api/context
```

### Next.js không build được

```bash
# Rebuild container
docker-compose build --no-cache cs-agent
docker-compose up cs-agent
```

## Customization

### Thay đổi ports

Sửa file `docker-compose.yml`:

```yaml
ports:
  - "8080:3000"  # Web interface trên port 8080
  - "8124:8123"  # PathRAG API trên port 8124
```

### Mount knowledge base từ host

```yaml
volumes:
  - ./my-knowledge-base:/app/pathrag/knowledgebase:ro
```

### Sử dụng external database

Thay đổi biến môi trường `POSTGRES_URL` trong `.env` để trỏ đến database external.

## Production Deployment

### 1. Sử dụng với reverse proxy (nginx)

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /pathrag/ {
        proxy_pass http://localhost:8123/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 2. Environment variables cho production

```bash
# .env.production
NODE_ENV=production
NEXTAUTH_URL=https://your-domain.com
PATHRAG_API_URL=http://localhost:8123/api/context
```

### 3. Health check

Container có built-in health check. Kiểm tra:

```bash
docker-compose ps
# Sẽ hiển thị status "healthy" khi container hoạt động tốt
```

## Backup và Restore

### Backup knowledge base

```bash
docker-compose exec cs-agent tar -czf /tmp/knowledgebase-backup.tar.gz -C /app/pathrag knowledgebase
docker cp $(docker-compose ps -q cs-agent):/tmp/knowledgebase-backup.tar.gz ./
```

### Restore knowledge base

```bash
docker cp ./knowledgebase-backup.tar.gz $(docker-compose ps -q cs-agent):/tmp/
docker-compose exec cs-agent tar -xzf /tmp/knowledgebase-backup.tar.gz -C /app/pathrag
```
